
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/util/Tools.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '617b5KPPRpFBp3JOQImy4wR', 'Tools');
// scripts/util/Tools.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tools = void 0;
var AudioManager_1 = require("./AudioManager");
var Config_1 = require("./Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var Tools = /** @class */ (function () {
    function Tools() {
    }
    Tools.setTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction);
    };
    Tools.setGameTouchEvent = function (peopleNode, startFunction, endFunction, cancelFunction) {
        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction);
    };
    //添加点击事件 
    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要
    //peopleNode 节点
    //startFunction 按下事件
    //endFunction 抬起事件
    //cancelFunction 取消事件
    Tools.setTouchEventParent = function (peopleNode, isSound, startFunction, endFunction, cancelFunction) {
        peopleNode.on(cc.Node.EventType.TOUCH_START, function (event) {
            if (isSound) {
                AudioManager_1.AudioManager.keyingToneAudio();
            }
            if (startFunction != null) {
                startFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            if (endFunction != null) {
                endFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, function (event) {
            if (cancelFunction != null) {
                cancelFunction(peopleNode, event);
            }
        }, this);
    };
    Tools.cancelTouchStartListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_START, this);
    };
    Tools.cancelTouchEndListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_END, this);
    };
    Tools.cancelTouchCancelListener = function (peopleNode) {
        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);
    };
    //为精灵添加图片
    Tools.setNodeSpriteFrame = function (node, path) {
        cc.resources.load(path, cc.SpriteFrame, function (error, assets) {
            var sprite = node.getComponent(cc.Sprite);
            sprite.spriteFrame = assets;
        });
    };
    //添加网络图片
    Tools.setNodeSpriteFrameUrl = function (node, url) {
        if (!node) {
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
        });
    };
    // 设置备用纹理
    Tools.setFallbackTexture = function (sprite) {
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var color = [150, 150, 150, 255]; // 灰色
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
    };
    // 异步加载网络图片（带回调）
    Tools.setNodeSpriteFrameUrlAsync = function (node, url, onComplete) {
        if (!node) {
            console.error("❌ 节点为null，无法设置图片");
            if (onComplete)
                onComplete(false);
            return;
        }
        var avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }
        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            if (onComplete)
                onComplete(false);
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u56FE\u7247\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                if (onComplete)
                    onComplete(false);
                return;
            }
            texture.setPremultiplyAlpha(true); // 👈 关键设置
            texture.packable = false; //加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            node.active = true;
            node.opacity = 255;
            if (onComplete)
                onComplete(true);
        });
    };
    //红色按钮
    Tools.redButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnRedNormal, Config_1.Config.btnRedPressed, Config_1.Config.btnRedNormalColor, Config_1.Config.btnRedPressedColor, click, label);
    };
    //绿色按钮
    Tools.greenButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGreenNormal, Config_1.Config.btnGreenPressed, Config_1.Config.btnGreenNormalColor, Config_1.Config.btnGreenPressedColor, click, label);
    };
    //黄色按钮
    Tools.yellowButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnYellowNormal, Config_1.Config.btnYellowPressed, Config_1.Config.btnYellowNormalColor, Config_1.Config.btnYellowPressedColor, click, label);
    };
    //灰色按钮
    Tools.grayButton = function (node, click, label) {
        Tools.buttonState(node, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormal, Config_1.Config.btnGrayNormalColor, Config_1.Config.btnGrayNormalColor, click, label);
    };
    //通用的按钮点击事件，带点击变颜色的
    Tools.buttonState = function (node, normalImg, pressedImg, normalColor, pressedColor, click, labelText) {
        var btnGreen = node.getChildByName('btn_color_normal'); //获取按钮背景节点
        var btnLabel = node.getChildByName('button_label'); //获取按钮文字节点
        var label = btnLabel.getComponent(cc.Label);
        var labelOutline = btnLabel.getComponent(cc.LabelOutline);
        if (labelText != null) {
            label.string = labelText;
        }
        Tools.setTouchEvent(btnGreen, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
            label.fontSize = 34;
            label.lineHeight = 34;
            var color = new cc.Color();
            cc.Color.fromHEX(color, pressedColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#B3B3B3');
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
            if (click != null) {
                click();
            }
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight = 36;
            var color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(), '#FFFFFF');
        });
    };
    //点击变颜色的图片按钮
    Tools.imageButtonClick = function (node, normalImg, pressedImg, click) {
        Tools.setTouchEvent(node, function (node) {
            Tools.setNodeSpriteFrame(node, pressedImg);
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
            click();
        }, function (node) {
            Tools.setNodeSpriteFrame(node, normalImg);
        });
    };
    //格式化资金显示格式的
    Tools.NumToTBMK = function (num, digit, min) {
        var _a;
        if (digit === void 0) { digit = 1; }
        if (min === void 0) { min = 10000; }
        var intNum = num;
        if (intNum < min) {
            return intNum.toString();
        }
        var unitStrArr = ["T", "B", "M", "K"];
        var unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];
        for (var i = 0; i < unitArr.length; ++i) {
            var result = intNum / unitArr[i];
            if (result >= 1) {
                var str = result.toString();
                var strArr = str.split(".");
                var suffix = (_a = strArr[1]) !== null && _a !== void 0 ? _a : "";
                if (suffix.length >= digit) {
                    if (digit == 0) {
                        return strArr[0] + unitStrArr[i];
                    }
                    return strArr[0] + "." + suffix.substring(0, digit) + unitStrArr[i];
                }
                else {
                    var fillStr = new Array(digit - suffix.length).fill("0").join("");
                    return strArr[0] + "." + suffix + fillStr + unitStrArr[i];
                }
            }
        }
    };
    Tools.getCurrentTimeWithMilliseconds = function () {
        var currentDate = new Date();
        var year = currentDate.getFullYear();
        var month = String(currentDate.getMonth() + 1).padStart(2, '0');
        var day = String(currentDate.getDate()).padStart(2, '0');
        var hours = String(currentDate.getHours()).padStart(2, '0');
        var minutes = String(currentDate.getMinutes()).padStart(2, '0');
        var seconds = String(currentDate.getSeconds()).padStart(2, '0');
        var milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');
        return year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds + "." + milliseconds;
    };
    //赋值文本到剪切板
    Tools.copyToClipboard = function (text) {
        var textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            var successful = document.execCommand('copy');
            if (successful) {
                console.log('文本已复制到剪切板');
            }
            else {
                console.error('复制到剪切板失败');
            }
        }
        catch (err) {
            console.error('复制到剪切板失败：', err);
        }
        document.body.removeChild(textarea);
    };
    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)
    Tools.chunkArray = function (arr, chunkSize) {
        var result = [];
        for (var i = 0; i < arr.length; i += chunkSize) {
            result.push(arr.slice(i, i + chunkSize));
        }
        return result;
    };
    //设置倒计时的秒数的位置（使用固定位置逻辑，避免位置随文字变化而移动）
    Tools.setCountDownTimeLabel = function (buttonNode) {
        if (!buttonNode || !buttonNode.isValid) {
            console.warn("Tools.setCountDownTimeLabel: 按钮节点无效或为空");
            return;
        }
        var btn = buttonNode.getChildByName('button_label');
        var timeBtn = buttonNode.getChildByName('buttonLabel_time');
        if (!btn) {
            console.warn("Tools.setCountDownTimeLabel: 找不到 button_label 子节点");
            return;
        }
        if (!timeBtn) {
            console.warn("Tools.setCountDownTimeLabel: 找不到 buttonLabel_time 子节点");
            return;
        }
        // 设置锚点为左对齐，确保左括号位置固定
        timeBtn.anchorX = 0;
        timeBtn.anchorY = 0.5;
        // 计算固定位置：back文字右边缘 + 小间距，左括号固定在此位置
        var fixedLeftPos = btn.position.x + btn.width / 2 + 5; // 5像素间距
        timeBtn.setPosition(fixedLeftPos, 0);
        console.log("Tools.setCountDownTimeLabel: 倒计时位置已设置为固定位置", {
            btnPos: btn.position,
            btnWidth: btn.width,
            fixedLeftPos: fixedLeftPos,
            timeBtnPos: timeBtn.position
        });
    };
    return Tools;
}());
exports.Tools = Tools;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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