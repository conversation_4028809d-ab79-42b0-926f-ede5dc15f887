
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/bean/GameBean.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '8d8d2RWlP1Coqs84m843tJg', 'GameBean');
// scripts/bean/GameBean.ts

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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